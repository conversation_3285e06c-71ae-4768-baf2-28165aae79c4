-- 预警推送去重日志表
-- 其中 qymc、jbxx_id 仅 level = 2 有值
-- 其中 response、err_msg 仅 level = 1 有值
CREATE TABLE `tbl_gj_push_dedup_log` (
  `id` bigint(50) NOT NULL AUTO_INCREMENT COMMENT '主键，自动递增',
  `batch_id` varchar(64) NOT NULL COMMENT '批次ID，同一次推送的唯一标识',
  `level` tinyint(4) NOT NULL COMMENT '记录层级：1-真实请求记录，2-企业参数记录',
  `warn_model` tinyint(4) NOT NULL COMMENT '预警模型：1-模型1，2-模型2',
  `qymc` varchar(255) DEFAULT NULL COMMENT '企业名称（用于模型1去重）',
  `jbxx_id` varchar(64) DEFAULT NULL COMMENT '企业基本信息ID（用于模型2去重，对应view_cq_jbxxb.id）',
  `request` longtext COMMENT '请求参数（level=1时为完整请求，level=2时为企业数据字符串）',
  `response` varchar(200) COMMENT '响应内容',
  `status` tinyint(4) DEFAULT NULL COMMENT '推送状态：1-成功，2-失败',
  `err_msg` text COMMENT '错误信息',
  `push_time` datetime NOT NULL COMMENT '推送时间',
  PRIMARY KEY (`id`),
  KEY `idx_batch_id` (`batch_id`),
  KEY `idx_warn_model_qymc` (`warn_model`, `qymc`),
  KEY `idx_warn_model_jbxx_id` (`warn_model`, `jbxx_id`)
) COMMENT='预警模型推送日志表';
